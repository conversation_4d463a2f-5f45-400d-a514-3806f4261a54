/**
 * 清理功能测试脚本
 * 用于测试定时清理功能是否正常工作
 */

// 测试清理统计信息API
async function testCleanupStats() {
  console.log('测试清理统计信息API...');
  
  try {
    const response = await fetch('/api/cleanup/stats');
    const stats = await response.json();
    
    console.log('清理统计信息:', stats);
    
    if (stats.expiredEmailAddresses !== undefined) {
      console.log('✅ 清理统计信息API正常工作');
    } else {
      console.log('❌ 清理统计信息API返回格式错误');
    }
  } catch (error) {
    console.error('❌ 清理统计信息API测试失败:', error);
  }
}

// 测试手动清理API
async function testManualCleanup() {
  console.log('测试手动清理API...');
  
  try {
    const response = await fetch('/api/cleanup/manual', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        cleanupEmailAddresses: true,
        cleanupEmails: true,
        cleanupAttachments: true,
        forceCleanup: false
      })
    });
    
    const result = await response.json();
    
    console.log('手动清理结果:', result);
    
    if (result.deletedEmailAddresses !== undefined) {
      console.log('✅ 手动清理API正常工作');
    } else {
      console.log('❌ 手动清理API返回格式错误');
    }
  } catch (error) {
    console.error('❌ 手动清理API测试失败:', error);
  }
}

// 创建测试数据
async function createTestData() {
  console.log('创建测试数据...');
  
  try {
    // 生成一个测试邮箱
    const emailResponse = await fetch('/api/generate-email', {
      method: 'POST'
    });
    const emailData = await emailResponse.json();
    
    console.log('创建的测试邮箱:', emailData.email);
    
    // 注意：在实际环境中，您需要发送一封测试邮件到这个地址
    // 这里只是演示如何创建邮箱地址
    
    return emailData.email;
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    return null;
  }
}

// 运行所有测试
async function runTests() {
  console.log('开始清理功能测试...\n');
  
  // 创建测试数据
  const testEmail = await createTestData();
  console.log('');
  
  // 测试统计信息
  await testCleanupStats();
  console.log('');
  
  // 测试手动清理
  await testManualCleanup();
  console.log('');
  
  console.log('清理功能测试完成！');
  
  if (testEmail) {
    console.log(`\n提示: 您可以向 ${testEmail} 发送测试邮件来测试完整的邮件处理流程`);
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 添加到全局作用域以便在控制台中调用
  window.testCleanup = runTests;
  window.testCleanupStats = testCleanupStats;
  window.testManualCleanup = testManualCleanup;
  window.createTestData = createTestData;
  
  console.log('清理测试函数已加载到全局作用域:');
  console.log('- testCleanup(): 运行所有测试');
  console.log('- testCleanupStats(): 测试统计信息API');
  console.log('- testManualCleanup(): 测试手动清理API');
  console.log('- createTestData(): 创建测试数据');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runTests,
    testCleanupStats,
    testManualCleanup,
    createTestData
  };
}
