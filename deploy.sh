#!/bin/bash

# 临时邮箱系统部署脚本
# 遵循Google Shell风格指南

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查部署前置条件..."
    
    if ! command -v wrangler &> /dev/null; then
        log_error "Wrangler CLI 未安装。请运行: npm install -g wrangler"
        exit 1
    fi
    
    if ! wrangler whoami &> /dev/null; then
        log_error "未登录 Cloudflare。请运行: wrangler auth login"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 创建D1数据库
create_database() {
    log_info "创建D1数据库..."
    
    local db_name="temp-email-db"
    
    # 检查数据库是否已存在
    if wrangler d1 list | grep -q "$db_name"; then
        log_warning "数据库 $db_name 已存在，跳过创建"
    else
        log_info "创建新数据库: $db_name"
        wrangler d1 create "$db_name"
    fi
    
    # 获取数据库ID
    local db_id
    db_id=$(wrangler d1 list | grep "$db_name" | awk '{print $2}')
    
    if [[ -z "$db_id" ]]; then
        log_error "无法获取数据库ID"
        exit 1
    fi
    
    log_success "数据库创建完成，ID: $db_id"
    
    # 更新wrangler.toml中的数据库ID
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/database_id = \"your-database-id\"/database_id = \"$db_id\"/" backend/wrangler.toml
    else
        # Linux
        sed -i "s/database_id = \"your-database-id\"/database_id = \"$db_id\"/" backend/wrangler.toml
    fi
    
    log_success "已更新wrangler.toml中的数据库ID"
}

# 初始化数据库表结构
init_database_schema() {
    log_info "初始化数据库表结构..."
    
    cd backend
    wrangler d1 execute temp-email-db --file=migrations/schema.sql
    cd ..
    
    log_success "数据库表结构初始化完成"
}

# 创建R2存储桶
create_r2_bucket() {
    log_info "创建R2存储桶..."
    
    local bucket_name="temp-email-attachments"
    
    # 检查存储桶是否已存在
    if wrangler r2 bucket list | grep -q "$bucket_name"; then
        log_warning "存储桶 $bucket_name 已存在，跳过创建"
    else
        log_info "创建新存储桶: $bucket_name"
        wrangler r2 bucket create "$bucket_name"
        log_success "存储桶创建完成"
    fi
}

# 部署Worker
deploy_worker() {
    log_info "部署Cloudflare Worker..."
    
    cd backend
    wrangler deploy
    cd ..
    
    log_success "Worker部署完成"
}

# 部署前端到Pages
deploy_frontend() {
    log_info "部署前端到Cloudflare Pages..."
    
    cd frontend
    wrangler pages deploy . --project-name=temp-email-frontend
    cd ..
    
    log_success "前端部署完成"
}

# 配置邮件路由提示
show_email_routing_instructions() {
    log_info "邮件路由配置说明:"
    echo ""
    echo "请在Cloudflare Dashboard中配置邮件路由:"
    echo "1. 进入您的域名设置"
    echo "2. 点击 'Email' -> 'Email Routing'"
    echo "3. 添加路由规则:"
    echo "   - 匹配: *@your-domain.com"
    echo "   - 动作: Send to Worker"
    echo "   - Worker: temp-email-worker"
    echo ""
    log_warning "请将 'your-domain.com' 替换为您的实际域名"
}

# 显示部署后的配置说明
show_post_deployment_instructions() {
    log_info "部署完成后的配置说明:"
    echo ""
    echo "1. 更新 backend/wrangler.toml 中的配置:"
    echo "   - DOMAIN: 设置为您的域名"
    echo "   - CORS_ORIGIN: 设置为前端Pages的URL"
    echo ""
    echo "2. 重新部署Worker以应用配置:"
    echo "   cd backend && wrangler deploy"
    echo ""
    echo "3. 配置邮件路由（见上方说明）"
    echo ""
    log_success "所有组件部署完成！"
}

# 主函数
main() {
    log_info "开始部署临时邮箱系统..."
    
    check_prerequisites
    create_database
    init_database_schema
    create_r2_bucket
    deploy_worker
    deploy_frontend
    
    echo ""
    show_email_routing_instructions
    echo ""
    show_post_deployment_instructions
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
