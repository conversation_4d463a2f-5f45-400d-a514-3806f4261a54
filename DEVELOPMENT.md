# 开发环境配置指南

本文档说明如何设置本地开发环境。

## 环境要求

- Node.js 18+
- npm 或 yarn
- Wrangler CLI
- Git

## 本地开发设置

### 1. 克隆项目
```bash
git clone <repository-url>
cd temp-email-system
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置本地环境

#### 创建本地D1数据库
```bash
cd backend
wrangler d1 create temp-email-db-local
```

#### 更新本地配置
在 `backend/wrangler.toml` 中添加本地环境配置：
```toml
[[env.local.d1_databases]]
binding = "DB"
database_name = "temp-email-db-local"
database_id = "your-local-database-id"
```

#### 初始化本地数据库
```bash
wrangler d1 execute temp-email-db-local --file=migrations/schema.sql
```

### 4. 启动开发服务器

#### 启动Worker开发服务器
```bash
cd backend
wrangler dev --env local
```

#### 启动前端开发服务器
```bash
cd frontend
python3 -m http.server 8080
# 或者使用 Node.js
npx serve -p 8080
```

## 开发工作流

### 代码风格

项目遵循Google代码风格指南：

#### JavaScript
- 使用2空格缩进
- 使用单引号
- 函数和变量使用camelCase
- 类名使用PascalCase
- 常量使用UPPER_SNAKE_CASE

#### CSS
- 使用2空格缩进
- 类名使用kebab-case
- 遵循BEM命名规范（可选）

#### HTML
- 使用2空格缩进
- 属性值使用双引号
- 语义化标签

### 提交规范

使用Conventional Commits规范：

```
feat: 添加新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

示例：
```bash
git commit -m "feat: 添加邮件搜索功能"
git commit -m "fix: 修复附件下载问题"
```

## 测试

### 单元测试
```bash
npm test
```

### 集成测试
```bash
npm run test:integration
```

### 手动测试
1. 启动开发服务器
2. 在浏览器中打开 `http://localhost:8080`
3. 运行测试脚本：
```javascript
// 在浏览器控制台中运行
testCleanup()
```

## 调试

### Worker调试
```bash
# 查看Worker日志
wrangler tail temp-email-worker --env local

# 调试模式启动
wrangler dev --env local --debug
```

### 数据库调试
```bash
# 查看数据库内容
wrangler d1 execute temp-email-db-local --command="SELECT * FROM emails LIMIT 10"

# 查看表结构
wrangler d1 execute temp-email-db-local --command=".schema"
```

### 前端调试
- 使用浏览器开发者工具
- 检查网络请求
- 查看控制台日志

## 常用开发命令

```bash
# 安装依赖
npm install

# 启动前端开发服务器
npm run dev:frontend

# 启动Worker开发服务器
npm run dev:worker

# 部署到开发环境
npm run deploy:dev

# 运行测试
npm test

# 代码格式化
npm run format

# 代码检查
npm run lint

# 构建生产版本
npm run build
```

## 环境变量

### 开发环境变量
在 `backend/wrangler.toml` 的 `[env.local.vars]` 部分：

```toml
[env.local.vars]
DOMAIN = "localhost"
CORS_ORIGIN = "http://localhost:8080"
DEBUG = "true"
```

### 生产环境变量
在 `backend/wrangler.toml` 的 `[vars]` 部分：

```toml
[vars]
DOMAIN = "your-domain.com"
CORS_ORIGIN = "https://your-frontend.pages.dev"
DEBUG = "false"
```

## 数据库迁移

### 创建新迁移
```bash
# 创建迁移文件
touch backend/migrations/002_add_new_feature.sql
```

### 应用迁移
```bash
# 本地环境
wrangler d1 execute temp-email-db-local --file=backend/migrations/002_add_new_feature.sql

# 生产环境
wrangler d1 execute temp-email-db --file=backend/migrations/002_add_new_feature.sql
```

## 性能监控

### 本地性能测试
```bash
# 使用curl测试API性能
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8787/api/generate-email"
```

### 生产环境监控
- 使用Cloudflare Analytics
- 监控Worker执行时间
- 跟踪数据库查询性能

## 故障排除

### 常见开发问题

#### 1. Worker无法启动
- 检查wrangler.toml配置
- 确认数据库连接
- 查看错误日志

#### 2. 前端无法连接API
- 检查CORS设置
- 确认API端点URL
- 检查网络连接

#### 3. 数据库连接失败
- 确认数据库ID正确
- 检查权限设置
- 重新创建本地数据库

#### 4. 邮件解析错误
- 检查邮件格式
- 查看解析器日志
- 测试不同邮件类型

### 调试技巧

1. **使用console.log**: 在关键位置添加日志
2. **断点调试**: 使用浏览器调试工具
3. **网络监控**: 检查API请求和响应
4. **数据库查询**: 直接查询数据库验证数据

## 贡献指南

### 提交Pull Request
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码审查
- 确保代码符合风格指南
- 添加必要的测试
- 更新相关文档
- 通过所有检查

### 发布流程
1. 更新版本号
2. 更新CHANGELOG
3. 创建Git标签
4. 部署到生产环境
