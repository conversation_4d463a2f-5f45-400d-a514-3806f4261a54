# 临时邮箱系统部署指南

本文档详细说明如何部署临时邮箱系统到Cloudflare平台。

## 前置要求

### 1. 账户和工具
- Cloudflare账户（免费版即可）
- 已验证的域名（添加到Cloudflare）
- Node.js 18+ 和 npm
- Git

### 2. 安装Wrangler CLI
```bash
npm install -g wrangler
```

### 3. 登录Cloudflare
```bash
wrangler auth login
```

## 快速部署

### 自动部署（推荐）
运行自动部署脚本：
```bash
./deploy.sh
```

### 手动部署
如果自动部署失败，可以按以下步骤手动部署：

#### 1. 创建D1数据库
```bash
wrangler d1 create temp-email-db
```

记录返回的数据库ID，更新 `backend/wrangler.toml` 中的 `database_id`。

#### 2. 初始化数据库表结构
```bash
cd backend
wrangler d1 execute temp-email-db --file=migrations/schema.sql
cd ..
```

#### 3. 创建R2存储桶
```bash
wrangler r2 bucket create temp-email-attachments
```

#### 4. 部署Worker
```bash
cd backend
wrangler deploy
cd ..
```

#### 5. 部署前端
```bash
cd frontend
wrangler pages deploy . --project-name=temp-email-frontend
cd ..
```

## 配置说明

### 1. 更新Worker配置
编辑 `backend/wrangler.toml`：

```toml
[vars]
DOMAIN = "your-domain.com"  # 替换为您的域名
CORS_ORIGIN = "https://your-frontend-domain.pages.dev"  # 替换为前端Pages URL
```

### 2. 配置邮件路由
在Cloudflare Dashboard中：

1. 进入您的域名设置
2. 点击 **Email** → **Email Routing**
3. 启用Email Routing
4. 添加路由规则：
   - **匹配条件**: `*@your-domain.com`
   - **动作**: Send to Worker
   - **Worker**: `temp-email-worker`

### 3. 更新前端API配置
编辑 `frontend/_redirects`，将Worker URL替换为实际的Worker域名：
```
/api/* https://temp-email-worker.your-account.workers.dev/api/:splat 200
```

### 4. 重新部署应用配置更改
```bash
cd backend && wrangler deploy
cd ../frontend && wrangler pages deploy .
```

## 验证部署

### 1. 检查Worker状态
```bash
wrangler tail temp-email-worker
```

### 2. 测试API端点
访问以下URL检查API是否正常：
- `https://your-worker.workers.dev/api/cleanup/stats`

### 3. 测试前端
访问Pages URL，应该能看到临时邮箱界面。

### 4. 测试邮件接收
1. 在前端生成一个临时邮箱地址
2. 向该地址发送测试邮件
3. 检查邮件是否在前端显示

## 监控和维护

### 1. 查看Worker日志
```bash
wrangler tail temp-email-worker
```

### 2. 查看数据库状态
```bash
wrangler d1 execute temp-email-db --command="SELECT COUNT(*) FROM emails"
```

### 3. 查看存储使用情况
在Cloudflare Dashboard的R2部分查看存储桶使用情况。

### 4. 手动触发清理
```bash
curl -X POST https://your-worker.workers.dev/api/cleanup/manual \
  -H "Content-Type: application/json" \
  -d '{"cleanupEmailAddresses": true, "cleanupEmails": true}'
```

## 故障排除

### 常见问题

#### 1. Worker部署失败
- 检查wrangler.toml配置是否正确
- 确认数据库ID和存储桶名称正确
- 检查账户权限

#### 2. 邮件无法接收
- 确认邮件路由配置正确
- 检查域名DNS设置
- 查看Worker日志确认邮件是否到达

#### 3. 前端无法连接API
- 检查CORS配置
- 确认_redirects文件中的Worker URL正确
- 检查网络连接

#### 4. 附件无法下载
- 确认R2存储桶权限
- 检查附件上传是否成功
- 查看Worker日志

### 日志分析
Worker日志中的关键信息：
- `Email processed successfully`: 邮件处理成功
- `Invalid recipient`: 收件人无效
- `Cleanup completed`: 清理任务完成

## 成本估算

基于Cloudflare的免费和付费计划：

### 免费计划限制
- Workers: 100,000 请求/天
- D1: 5GB 存储，25M 行读取/天
- R2: 10GB 存储，1M Class A操作/月
- Pages: 无限静态请求

### 预估成本（中等使用量）
- Workers: $5/月（1M 请求）
- D1: $5/月（额外存储和操作）
- R2: $4.50/月（100GB 存储）
- 总计: ~$15/月

## 安全建议

1. **定期更新依赖**: 保持Wrangler和其他工具最新
2. **监控使用量**: 设置账单警报
3. **备份数据**: 定期导出重要数据
4. **访问控制**: 限制Worker和存储桶的访问权限
5. **日志监控**: 定期检查异常日志

## 扩展功能

### 可选增强功能
1. **邮件搜索**: 添加全文搜索功能
2. **邮件转发**: 支持邮件转发到真实邮箱
3. **API密钥**: 添加API访问控制
4. **多域名支持**: 支持多个域名
5. **邮件模板**: 支持HTML邮件模板

### 性能优化
1. **缓存策略**: 实现邮件列表缓存
2. **分页加载**: 大量邮件时的分页处理
3. **压缩存储**: 邮件内容压缩存储
4. **CDN优化**: 静态资源CDN加速
