{"name": "temp-email-system", "version": "1.0.0", "description": "临时邮箱系统 - 前端和后端", "scripts": {"dev:frontend": "cd frontend && python3 -m http.server 8080", "dev:worker": "cd backend && wrangler dev", "deploy:frontend": "cd frontend && wrangler pages deploy .", "deploy:worker": "cd backend && wrangler deploy", "setup:db": "cd backend && wrangler d1 execute temp-email-db --file=migrations/schema.sql"}, "keywords": ["temporary-email", "cloudflare", "worker", "pages"], "author": "huahai", "license": "MIT", "devDependencies": {"wrangler": "^3.114.12"}}