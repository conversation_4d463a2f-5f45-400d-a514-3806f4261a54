/**
 * 临时邮箱前端JavaScript逻辑
 * 遵循Google JavaScript风格指南
 */

class TempEmailApp {
  constructor() {
    this.apiBaseUrl = '/api'; // Cloudflare Worker API 端点
    this.currentEmail = '';
    this.emails = [];
    this.refreshInterval = null;
    
    this.init();
  }

  /**
   * 初始化应用
   */
  init() {
    this.bindEvents();
    this.generateEmail();
    this.startAutoRefresh();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 刷新邮箱按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.generateEmail();
    });

    // 复制邮箱地址按钮
    document.getElementById('copyBtn').addEventListener('click', () => {
      this.copyEmailAddress();
    });

    // 关闭模态框
    document.getElementById('closeModal').addEventListener('click', () => {
      this.closeModal();
    });

    // 点击模态框背景关闭
    document.getElementById('emailModal').addEventListener('click', (e) => {
      if (e.target.id === 'emailModal') {
        this.closeModal();
      }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeModal();
      }
    });
  }

  /**
   * 生成新的临时邮箱地址
   */
  async generateEmail() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/generate-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('生成邮箱地址失败');
      }

      const data = await response.json();
      this.currentEmail = data.email;
      
      document.getElementById('emailAddress').value = this.currentEmail;
      
      // 清空邮件列表
      this.emails = [];
      this.renderEmails();
      
      this.showNotification('新邮箱地址已生成', 'success');
    } catch (error) {
      console.error('生成邮箱地址失败:', error);
      this.showNotification('生成邮箱地址失败，请重试', 'error');
    }
  }

  /**
   * 复制邮箱地址到剪贴板
   */
  async copyEmailAddress() {
    if (!this.currentEmail) {
      this.showNotification('暂无邮箱地址可复制', 'error');
      return;
    }

    try {
      await navigator.clipboard.writeText(this.currentEmail);
      this.showNotification('邮箱地址已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：使用传统方法复制
      this.fallbackCopyTextToClipboard(this.currentEmail);
    }
  }

  /**
   * 降级复制方法
   * @param {string} text 要复制的文本
   */
  fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      if (successful) {
        this.showNotification('邮箱地址已复制到剪贴板', 'success');
      } else {
        this.showNotification('复制失败，请手动复制', 'error');
      }
    } catch (error) {
      console.error('降级复制也失败:', error);
      this.showNotification('复制失败，请手动复制', 'error');
    }

    document.body.removeChild(textArea);
  }

  /**
   * 获取邮件列表
   */
  async fetchEmails() {
    if (!this.currentEmail) {
      return;
    }

    try {
      const response = await fetch(`${this.apiBaseUrl}/emails/${encodeURIComponent(this.currentEmail)}`);
      
      if (!response.ok) {
        throw new Error('获取邮件失败');
      }

      const data = await response.json();
      this.emails = data.emails || [];
      this.renderEmails();
    } catch (error) {
      console.error('获取邮件失败:', error);
      // 静默失败，不显示错误通知
    }
  }

  /**
   * 渲染邮件列表
   */
  renderEmails() {
    const container = document.getElementById('emailsContainer');
    const countElement = document.getElementById('emailCount');
    
    countElement.textContent = this.emails.length;

    if (this.emails.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">📭</div>
          <h3>暂无邮件</h3>
          <p>发送邮件到上方地址，邮件将自动显示在这里</p>
        </div>
      `;
      return;
    }

    const emailsHtml = this.emails.map(email => `
      <div class="email-item" onclick="app.openEmailModal('${email.id}')">
        <div class="email-sender">${this.escapeHtml(email.sender)}</div>
        <div class="email-subject">${this.escapeHtml(email.subject || '(无主题)')}</div>
        <div class="email-preview">${this.escapeHtml(email.preview || '')}</div>
        <div class="email-date">${this.formatDate(email.received_at)}</div>
      </div>
    `).join('');

    container.innerHTML = emailsHtml;
  }

  /**
   * 打开邮件详情模态框
   * @param {string} emailId 邮件ID
   */
  async openEmailModal(emailId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/email/${emailId}`);
      
      if (!response.ok) {
        throw new Error('获取邮件详情失败');
      }

      const email = await response.json();
      
      document.getElementById('modalSubject').textContent = email.subject || '(无主题)';
      document.getElementById('modalSender').textContent = email.sender;
      document.getElementById('modalRecipient').textContent = email.recipient;
      document.getElementById('modalDate').textContent = this.formatDate(email.received_at);
      document.getElementById('modalContent').innerHTML = email.content || '(无内容)';
      
      // 处理附件
      const attachmentsContainer = document.getElementById('modalAttachments');
      if (email.attachments && email.attachments.length > 0) {
        const attachmentsHtml = email.attachments.map(attachment => `
          <a href="${attachment.url}" class="attachment-item" target="_blank">
            <span class="attachment-icon">📎</span>
            ${this.escapeHtml(attachment.filename)} (${this.formatFileSize(attachment.size)})
          </a>
        `).join('');
        attachmentsContainer.innerHTML = `<h4>附件:</h4>${attachmentsHtml}`;
      } else {
        attachmentsContainer.innerHTML = '';
      }

      document.getElementById('emailModal').classList.add('show');
    } catch (error) {
      console.error('打开邮件详情失败:', error);
      this.showNotification('打开邮件详情失败', 'error');
    }
  }

  /**
   * 关闭模态框
   */
  closeModal() {
    document.getElementById('emailModal').classList.remove('show');
  }

  /**
   * 显示通知
   * @param {string} message 通知消息
   * @param {string} type 通知类型 ('success' | 'error')
   */
  showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notificationText');
    
    notificationText.textContent = message;
    notification.className = `notification ${type}`;
    notification.classList.add('show');

    setTimeout(() => {
      notification.classList.remove('show');
    }, 3000);
  }

  /**
   * 开始自动刷新邮件
   */
  startAutoRefresh() {
    // 立即获取一次邮件
    this.fetchEmails();
    
    // 每10秒刷新一次邮件
    this.refreshInterval = setInterval(() => {
      this.fetchEmails();
    }, 10000);
  }

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * HTML转义
   * @param {string} text 要转义的文本
   * @return {string} 转义后的文本
   */
  escapeHtml(text) {
    const map = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
  }

  /**
   * 格式化日期
   * @param {string} dateString 日期字符串
   * @return {string} 格式化后的日期
   */
  formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @return {string} 格式化后的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 初始化应用
const app = new TempEmailApp();

// 页面卸载时停止自动刷新
window.addEventListener('beforeunload', () => {
  app.stopAutoRefresh();
});
