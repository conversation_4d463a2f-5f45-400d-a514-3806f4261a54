<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱 - TempMail</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">📧 TempMail</h1>
                <p class="subtitle">安全、快速的临时邮箱服务</p>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 邮箱地址卡片 -->
            <div class="email-card">
                <div class="email-header">
                    <h2>您的临时邮箱地址</h2>
                    <button class="refresh-btn" id="refreshBtn" title="生成新邮箱">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                        </svg>
                    </button>
                </div>
                <div class="email-display">
                    <input type="text" id="emailAddress" readonly class="email-input" placeholder="正在生成邮箱地址...">
                    <button class="copy-btn" id="copyBtn" title="复制邮箱地址">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="m5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                        </svg>
                    </button>
                </div>
                <div class="email-info">
                    <p>此邮箱地址将在 24 小时后自动删除</p>
                </div>
            </div>

            <!-- 邮件列表 -->
            <div class="emails-section">
                <div class="section-header">
                    <h2>收件箱</h2>
                    <div class="email-count">
                        <span id="emailCount">0</span> 封邮件
                    </div>
                </div>
                
                <div class="emails-container" id="emailsContainer">
                    <div class="empty-state">
                        <div class="empty-icon">📭</div>
                        <h3>暂无邮件</h3>
                        <p>发送邮件到上方地址，邮件将自动显示在这里</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部 -->
        <footer class="footer">
            <p>&copy; 2024 TempMail. 保护您的隐私安全.</p>
        </footer>
    </div>

    <!-- 邮件详情模态框 -->
    <div class="modal" id="emailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalSubject">邮件主题</h3>
                <button class="close-btn" id="closeModal">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="email-meta">
                    <div class="meta-item">
                        <strong>发件人:</strong> <span id="modalSender"></span>
                    </div>
                    <div class="meta-item">
                        <strong>收件人:</strong> <span id="modalRecipient"></span>
                    </div>
                    <div class="meta-item">
                        <strong>时间:</strong> <span id="modalDate"></span>
                    </div>
                </div>
                <div class="email-content" id="modalContent">
                    <!-- 邮件内容将在这里显示 -->
                </div>
                <div class="email-attachments" id="modalAttachments">
                    <!-- 附件将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div class="notification" id="notification">
        <span id="notificationText"></span>
    </div>

    <script src="script.js"></script>
</body>
</html>
