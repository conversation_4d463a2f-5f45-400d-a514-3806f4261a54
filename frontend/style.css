/* 苹果设计风格的CSS样式 */

:root {
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    --background-color: #F2F2F7;
    --surface-color: #FFFFFF;
    --text-primary: #000000;
    --text-secondary: #8E8E93;
    --border-color: #C6C6C8;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --border-radius-small: 8px;
    --font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header-content {
    background: var(--surface-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 邮箱地址卡片 */
.email-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.email-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

.refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-small);
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

.email-display {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.email-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    font-family: 'SF Mono', Monaco, monospace;
    background: var(--background-color);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
}

.email-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.copy-btn {
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-small);
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-btn:hover {
    background: #28A745;
    transform: translateY(-1px);
}

.email-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
}

/* 邮件列表区域 */
.emails-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    flex: 1;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

.email-count {
    background: var(--background-color);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.emails-container {
    min-height: 200px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.empty-state p {
    font-size: 1rem;
    line-height: 1.5;
}

/* 邮件项目样式 */
.email-item {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: var(--border-radius-small);
    margin-bottom: 8px;
}

.email-item:hover {
    background-color: var(--background-color);
}

.email-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.email-sender {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.email-subject {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.email-preview {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.email-date {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* 底部样式 */
.footer {
    text-align: center;
    padding: 20px 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 40px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    max-width: 90%;
    max-height: 90%;
    width: 600px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-small);
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

.email-meta {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--background-color);
    border-radius: var(--border-radius-small);
}

.meta-item {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.meta-item:last-child {
    margin-bottom: 0;
}

.meta-item strong {
    color: var(--text-primary);
    font-weight: 600;
}

.email-content {
    line-height: 1.6;
    color: var(--text-primary);
    word-wrap: break-word;
}

.email-attachments {
    margin-top: 20px;
}

.attachment-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    margin-bottom: 8px;
    text-decoration: none;
    color: var(--primary-color);
    transition: background-color 0.2s ease;
}

.attachment-item:hover {
    background: var(--border-color);
}

.attachment-icon {
    margin-right: 8px;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: 12px 20px;
    border-radius: var(--border-radius-small);
    box-shadow: var(--shadow-medium);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1001;
    font-weight: 500;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    background: var(--error-color);
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .email-display {
        flex-direction: column;
    }

    .copy-btn {
        align-self: stretch;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .modal-content {
        width: 95%;
        max-height: 95%;
    }

    .modal-body {
        padding: 16px;
    }

    .notification {
        right: 16px;
        left: 16px;
        transform: translateY(-100%);
    }

    .notification.show {
        transform: translateY(0);
    }
}
