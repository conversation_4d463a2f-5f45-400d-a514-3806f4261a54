# Cloudflare Pages 头部配置
# 用于设置安全头部和缓存策略

/*
  # 安全头部
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # 内容安全策略
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self' https://*.workers.dev
  
  # 缓存控制
  Cache-Control: public, max-age=3600

# 静态资源缓存
/*.css
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Cache-Control: public, max-age=31536000, immutable

# HTML文件不缓存
/*.html
  Cache-Control: no-cache
