{"name": "rollup-plugin-node-polyfills", "version": "0.2.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/types/index.d.ts", "scripts": {"pretest": "npm run build", "test": "mocha", "prebuild": "rm -rf dist && mkdir dist", "build": "npm run build:constants && npm run build:deps && npm run build:bundlers", "build:bundlers": "tsc -p . && rollup -c", "build:deps": "node scripts/build-polyfills.js", "build:constants": "node scripts/build-constants.js", "release": "np --no-yarn --no-release-draft", "browser-test": "serve browser-test/dist", "prebrowser-test": "npm run build && node ./browser-test/index.js"}, "files": ["dist", "polyfills"], "keywords": ["rollup-plugin"], "author": "", "license": "MIT", "dependencies": {"rollup-plugin-inject": "^3.0.0"}, "devDependencies": {"browserify-fs": "^1.0.0", "buffer-es6": "^4.9.2", "crypto-browserify": "^3.11.0", "debug": "^4.1.1", "mocha": "^6.1.4", "np": "^5.0.3", "process-es6": "^0.11.2", "rollup": "^1.15.4", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-license": "^0.9.0", "rollup-plugin-node-resolve": "^5.0.2", "serve": "^11.0.1", "typescript": "^3.5.2"}, "repository": {"type": "git", "url": "**************:ionic-team/rollup-plugin-node-polyfills.git"}}