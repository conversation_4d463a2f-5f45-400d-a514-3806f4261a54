{"name": "unenv", "version": "2.0.0-rc.14", "description": "", "repository": "unjs/unenv", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"types": "./lib/index.d.mts", "default": "./dist/index.mjs"}, "./package.json": "./package.json", "./mock/proxy-cjs": {"types": "./lib/mock.d.cts", "default": "./lib/mock.cjs"}, "./mock/proxy-cjs/*": {"types": "./lib/mock.d.cts", "default": "./lib/mock.cjs"}, "./*": {"types": "./dist/runtime/*.d.mts", "default": "./dist/runtime/*.mjs"}}, "types": "./lib/index.d.mts", "files": ["dist", "lib"], "scripts": {"build": "pnpm node-ts ./scripts/build.ts", "build:watch": "pnpm node-ts --watch ./scripts/build.ts", "node-ts": "node --disable-warning=ExperimentalWarning --experimental-strip-types", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "pnpm node-ts ./test/node-coverage.ts && automd && eslint --fix . && prettier -w src test", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release --push --publish --prerelease --publishTag rc", "test": "pnpm lint && pnpm test:types && pnpm vitest --run", "test:types": "tsc --noEmit"}, "dependencies": {"defu": "^6.1.4", "exsolve": "^1.0.1", "ohash": "^2.0.10", "pathe": "^2.0.3", "ufo": "^1.5.4"}, "devDependencies": {"@parcel/watcher": "^2.5.1", "@types/node": "^22.13.9", "@vitest/coverage-v8": "^3.0.7", "automd": "^0.4.0", "changelogen": "^0.6.1", "consola": "^3.4.0", "esbuild": "^0.25.0", "eslint": "^9.21.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "magic-string": "^0.30.17", "oxc-parser": "^0.53.0", "oxc-resolver": "^5.0.0", "oxc-transform": "^0.53.0", "prettier": "^3.5.3", "rolldown": "^1.0.0-beta.3", "tinyexec": "^0.3.2", "typescript": "^5.8.2", "vitest": "^3.0.7", "workerd": "^1.20250224.0", "wrangler": "^3.111.0"}, "packageManager": "pnpm@10.5.2"}