import { notImplemented } from "../_internal/utils.mjs";
export const URL = globalThis.URL;
export const URLSearchParams = globalThis.URLSearchParams;
export const parseURL = /*@__PURE__*/ notImplemented("whatwg-url.parseURL");
export const basicURLParse = /*@__PURE__*/ notImplemented("whatwg-url.basicURLParse");
export const serializeURL = /*@__PURE__*/ notImplemented("whatwg-url.serializeURL");
export const serializeHost = /*@__PURE__*/ notImplemented("whatwg-url.serializeHost");
export const serializeInteger = /*@__PURE__*/ notImplemented("whatwg-url.serializeInteger");
export const serializeURLOrigin = /*@__PURE__*/ notImplemented("whatwg-url.serializeURLOrigin");
export const setTheUsername = /*@__PURE__*/ notImplemented("whatwg-url.setTheUsername");
export const setThePassword = /*@__PURE__*/ notImplemented("whatwg-url.setThePassword");
export const cannotHaveAUsernamePasswordPort = /*@__PURE__*/ notImplemented("whatwg-url.cannotHaveAUsernamePasswordPort");
export const percentDecodeBytes = /*@__PURE__*/ notImplemented("whatwg-url.percentDecodeBytes");
export const percentDecodeString = /*@__PURE__*/ notImplemented("whatwg-url.percentDecodeString");
