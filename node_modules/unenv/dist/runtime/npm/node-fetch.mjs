export const fetch = (...args) => globalThis.fetch(...args);
export const Headers = globalThis.Headers;
export const Request = globalThis.Request;
export const Response = globalThis.Response;
export const AbortController = globalThis.AbortController;
export const FetchError = Error;
export const AbortError = Error;
const redirectStatus = new Set([
	301,
	302,
	303,
	307,
	308
]);
export const isRedirect = (code) => redirectStatus.has(code);
fetch.Promise = globalThis.Promise;
fetch.isRedirect = isRedirect;
export default fetch;
