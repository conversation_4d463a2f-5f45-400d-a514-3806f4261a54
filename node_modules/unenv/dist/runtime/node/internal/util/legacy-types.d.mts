import type nodeUtil from "node:util";
export declare const isRegExp: typeof nodeUtil.isRegExp;
export declare const isDate: typeof nodeUtil.isDate;
export declare const isArray: typeof nodeUtil.isArray;
export declare const isBoolean: typeof nodeUtil.isBoolean;
export declare const isNull: typeof nodeUtil.isNull;
export declare const isNullOrUndefined: typeof nodeUtil.isNullOrUndefined;
export declare const isNumber: typeof nodeUtil.isNumber;
export declare const isString: typeof nodeUtil.isString;
export declare const isSymbol: typeof nodeUtil.isSymbol;
export declare const isUndefined: typeof nodeUtil.isUndefined;
export declare const isFunction: typeof nodeUtil.isFunction;
export declare const isBuffer: typeof nodeUtil.isBuffer;
export declare const isDeepStrictEqual: typeof nodeUtil.isDeepStrictEqual;
export declare const isObject: typeof nodeUtil.isObject;
export declare const isError: typeof nodeUtil.isError;
export declare const isPrimitive: typeof nodeUtil.isPrimitive;
