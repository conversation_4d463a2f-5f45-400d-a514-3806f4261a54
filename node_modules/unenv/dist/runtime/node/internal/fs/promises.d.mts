import type nodeFsPromises from "node:fs/promises";
export declare const access: unknown;
export declare const copyFile: unknown;
export declare const cp: unknown;
export declare const open: unknown;
export declare const opendir: unknown;
export declare const rename: unknown;
export declare const truncate: unknown;
export declare const rm: unknown;
export declare const rmdir: unknown;
export declare const mkdir: typeof nodeFsPromises.mkdir;
export declare const readdir: typeof nodeFsPromises.readdir;
export declare const readlink: typeof nodeFsPromises.readlink;
export declare const symlink: unknown;
export declare const lstat: typeof nodeFsPromises.lstat;
export declare const stat: typeof nodeFsPromises.stat;
export declare const link: unknown;
export declare const unlink: unknown;
export declare const chmod: unknown;
export declare const lchmod: unknown;
export declare const lchown: unknown;
export declare const chown: unknown;
export declare const utimes: unknown;
export declare const lutimes: unknown;
export declare const realpath: typeof nodeFsPromises.realpath;
export declare const mkdtemp: typeof nodeFsPromises.mkdtemp;
export declare const writeFile: unknown;
export declare const appendFile: unknown;
export declare const readFile: typeof nodeFsPromises.readFile;
export declare const watch: typeof nodeFsPromises.watch;
export declare const statfs: typeof nodeFsPromises.statfs;
export declare const glob: unknown;
