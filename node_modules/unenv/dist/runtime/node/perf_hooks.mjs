import { IntervalHistogram, RecordableHistogram } from "./internal/perf_hooks/histogram.mjs";
import { performance, Performance, PerformanceEntry, PerformanceMark, PerformanceMeasure, PerformanceObserverEntryList, PerformanceObserver, PerformanceResourceTiming } from "./internal/perf_hooks/performance.mjs";
export * from "./internal/perf_hooks/performance.mjs";
import { NODE_PERFORMANCE_GC_MAJOR, NODE_PERFORMANCE_GC_MINOR, NODE_PERFORMANCE_GC_INCREMENTAL, NODE_PERFORMANCE_GC_WEAKCB, NODE_PERFORMANCE_GC_FLAGS_NO, NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED, NODE_PERFORMANCE_GC_FLAGS_FORCED, NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING, NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE, NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY, NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE, NODE_PERFORMANCE_ENTRY_TYPE_GC, NODE_PERFORMANCE_ENTRY_TYPE_HTTP, NODE_PERFORMANCE_ENTRY_TYPE_HTTP2, NODE_PERFORMANCE_ENTRY_TYPE_NET, NODE_PERFORMANCE_ENTRY_TYPE_DNS, NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP, NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN, NODE_PERFORMANCE_MILESTONE_ENVIRONMENT, NODE_PERFORMANCE_MILESTONE_NODE_START, NODE_PERFORMANCE_MILESTONE_V8_START, NODE_PERFORMANCE_MILESTONE_LOOP_START, NODE_PERFORMANCE_MILESTONE_LOOP_EXIT, NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE } from "./internal/perf_hooks/constants.mjs";
export const constants = {
	NODE_PERFORMANCE_GC_MAJOR,
	NODE_PERFORMANCE_GC_MINOR,
	NODE_PERFORMANCE_GC_INCREMENTAL,
	NODE_PERFORMANCE_GC_WEAKCB,
	NODE_PERFORMANCE_GC_FLAGS_NO,
	NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED,
	NODE_PERFORMANCE_GC_FLAGS_FORCED,
	NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING,
	NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE,
	NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY,
	NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE,
	NODE_PERFORMANCE_ENTRY_TYPE_GC,
	NODE_PERFORMANCE_ENTRY_TYPE_HTTP,
	NODE_PERFORMANCE_ENTRY_TYPE_HTTP2,
	NODE_PERFORMANCE_ENTRY_TYPE_NET,
	NODE_PERFORMANCE_ENTRY_TYPE_DNS,
	NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP,
	NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN,
	NODE_PERFORMANCE_MILESTONE_ENVIRONMENT,
	NODE_PERFORMANCE_MILESTONE_NODE_START,
	NODE_PERFORMANCE_MILESTONE_V8_START,
	NODE_PERFORMANCE_MILESTONE_LOOP_START,
	NODE_PERFORMANCE_MILESTONE_LOOP_EXIT,
	NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE
};
export const monitorEventLoopDelay = function(_options) {
	return new IntervalHistogram();
};
export const createHistogram = function(_options) {
	return new RecordableHistogram();
};
export default {
	Performance,
	PerformanceMark,
	PerformanceEntry,
	PerformanceMeasure,
	PerformanceObserverEntryList,
	PerformanceObserver,
	PerformanceResourceTiming,
	performance,
	constants,
	createHistogram,
	monitorEventLoopDelay
};
