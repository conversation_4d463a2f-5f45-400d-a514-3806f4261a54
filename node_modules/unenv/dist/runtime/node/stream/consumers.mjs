import { notImplemented } from "../../_internal/utils.mjs";
export const arrayBuffer = /*@__PURE__*/ notImplemented("stream.consumers.arrayBuffer");
export const blob = /*@__PURE__*/ notImplemented("stream.consumers.blob");
export const buffer = /*@__PURE__*/ notImplemented("stream.consumers.buffer");
export const text = /*@__PURE__*/ notImplemented("stream.consumers.text");
export const json = /*@__PURE__*/ notImplemented("stream.consumers.json");
export default {
	arrayBuffer,
	blob,
	buffer,
	text,
	json
};
