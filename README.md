# 临时邮箱系统

一个基于 Cloudflare 技术栈的临时邮箱系统，包含前端和后端。

## 项目结构

```
temp-email-system/
├── frontend/                 # React 前端应用
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.js
├── backend/                  # Cloudflare Worker 后端
│   ├── src/
│   ├── wrangler.toml
│   └── package.json
├── database/                 # 数据库架构
│   └── schema.sql
└── README.md
```

## 技术栈

### 前端
- React 18
- Vite
- TypeScript
- Tailwind CSS (苹果风格设计)
- Cloudflare Pages 部署

### 后端
- Cloudflare Workers
- Cloudflare D1 数据库
- Cloudflare R2 存储
- Cloudflare Email Routing

## 功能特性

1. **邮件接收**: 通过 Cloudflare Email Routing 转发邮件到 Worker
2. **邮件存储**: 邮件内容存储在 D1 数据库，附件存储在 R2
3. **前端界面**: 简约的苹果风格 UI，支持邮件列表和详情查看
4. **附件处理**: 支持附件上传到 R2 和下载
5. **自动清理**: 每小时清理 24 小时前的数据

## 开发指南

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### 后端开发
```bash
cd backend
npm install
npx wrangler dev
```

## 部署

### 前端部署到 Cloudflare Pages
```bash
cd frontend
npm run build
# 通过 Cloudflare Pages 连接 Git 仓库自动部署
```

### 后端部署到 Cloudflare Workers
```bash
cd backend
npx wrangler deploy
```

## 配置

详细的配置说明请参考各子目录的 README 文件。
