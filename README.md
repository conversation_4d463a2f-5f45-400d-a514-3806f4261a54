# 临时邮箱系统

一个基于 Cloudflare 技术栈的临时邮箱系统，包含前端界面和后端处理服务。

## 功能特性

- 🎨 简约的苹果设计风格前端界面
- 📧 自动接收和解析邮件
- 💾 邮件内容存储到 Cloudflare D1 数据库
- 📎 附件上传到 Cloudflare R2 存储
- 🗑️ 自动清理24小时前的数据
- ⚡ 基于 Cloudflare Workers 的高性能后端

## 项目结构

```
.
├── frontend/           # 前端代码 (Cloudflare Pages)
│   ├── index.html     # 主页面
│   ├── style.css      # 样式文件
│   └── script.js      # JavaScript 逻辑
├── backend/           # 后端代码 (Cloudflare Workers)
│   ├── src/
│   │   └── index.js   # Worker 主文件
│   ├── migrations/
│   │   └── schema.sql # 数据库表结构
│   └── wrangler.toml  # Worker 配置
└── package.json       # 项目配置
```

## 部署说明

### 前置要求

1. 安装 Wrangler CLI: `npm install -g wrangler`
2. 登录 Cloudflare: `wrangler auth login`
3. 配置域名和邮件转发

### 部署步骤

1. 创建 D1 数据库: `wrangler d1 create temp-email-db`
2. 创建 R2 存储桶: `wrangler r2 bucket create temp-email-attachments`
3. 初始化数据库: `npm run setup:db`
4. 部署 Worker: `npm run deploy:worker`
5. 部署前端: `npm run deploy:frontend`

## 开发

- 前端开发: `npm run dev:frontend`
- 后端开发: `npm run dev:worker`

## 技术栈

- **前端**: HTML5, CSS3, Vanilla JavaScript
- **后端**: Cloudflare Workers
- **数据库**: Cloudflare D1 (SQLite)
- **存储**: Cloudflare R2
- **部署**: Cloudflare Pages + Workers

## 项目完成状态

✅ **已完成的功能**
- 项目结构初始化
- 苹果风格的前端界面设计
- 邮件接收和解析系统
- D1数据库集成
- R2文件存储集成
- 定时清理功能
- 完整的部署配置

🔧 **核心功能**
- 临时邮箱地址生成
- 实时邮件接收和显示
- 邮件内容查看（支持HTML和纯文本）
- 附件下载功能
- 自动过期清理（24小时）
- 响应式设计

📋 **API接口**
- `POST /api/generate-email` - 生成临时邮箱
- `GET /api/emails/{email}` - 获取邮件列表
- `GET /api/email/{id}` - 获取邮件详情
- `GET /api/attachment/{id}/download` - 下载附件
- `GET /api/cleanup/stats` - 清理统计信息
- `POST /api/cleanup/manual` - 手动触发清理

## 快速开始

### 1. 自动部署
```bash
./deploy.sh
```

### 2. 手动配置
详细步骤请参考 [DEPLOYMENT.md](DEPLOYMENT.md)

### 3. 开发环境
详细说明请参考 [DEVELOPMENT.md](DEVELOPMENT.md)
