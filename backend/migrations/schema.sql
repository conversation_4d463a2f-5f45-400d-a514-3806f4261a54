-- 临时邮箱系统数据库表结构
-- 遵循Google SQL风格指南

-- 邮箱地址表
CREATE TABLE IF NOT EXISTS email_addresses (
    id TEXT PRIMARY KEY,
    email_address TEXT UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- 邮件表
CREATE TABLE IF NOT EXISTS emails (
    id TEXT PRIMARY KEY,
    recipient TEXT NOT NULL,
    sender TEXT NOT NULL,
    subject TEXT,
    content_text TEXT,
    content_html TEXT,
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    message_id TEXT,
    in_reply_to TEXT,
    references TEXT,
    FOREIGN KEY (recipient) REFERENCES email_addresses(email_address)
);

-- 附件表
CREATE TABLE IF NOT EXISTS attachments (
    id TEXT PRIMARY KEY,
    email_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    content_type TEXT,
    size INTEGER,
    r2_key TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_email_addresses_email ON email_addresses(email_address);
CREATE INDEX IF NOT EXISTS idx_email_addresses_expires ON email_addresses(expires_at);
CREATE INDEX IF NOT EXISTS idx_emails_recipient ON emails(recipient);
CREATE INDEX IF NOT EXISTS idx_emails_received_at ON emails(received_at);
CREATE INDEX IF NOT EXISTS idx_attachments_email_id ON attachments(email_id);
