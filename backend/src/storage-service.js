/**
 * 存储服务
 * 处理与Cloudflare R2存储的交互
 */

export class StorageService {
  /**
   * 构造函数
   * @param {Object} r2 R2存储实例
   */
  constructor(r2) {
    this.r2 = r2;
  }

  /**
   * 上传附件到R2存储
   * @param {string} emailId 邮件ID
   * @param {string} filename 文件名
   * @param {string|ArrayBuffer} content 文件内容
   * @return {Promise<string>} R2对象键
   */
  async uploadAttachment(emailId, filename, content) {
    // 生成唯一的R2键
    const timestamp = Date.now();
    const randomId = this.generateRandomId();
    const sanitizedFilename = this.sanitizeFilename(filename);
    const r2Key = `attachments/${emailId}/${timestamp}-${randomId}-${sanitizedFilename}`;
    
    // 确定内容类型
    const contentType = this.getContentType(filename);
    
    // 转换内容为适当的格式
    let uploadContent;
    if (typeof content === 'string') {
      // 如果是字符串，假设是base64编码的二进制数据
      try {
        uploadContent = Uint8Array.from(atob(content), c => c.charCodeAt(0));
      } catch (error) {
        // 如果不是base64，直接作为文本上传
        uploadContent = new TextEncoder().encode(content);
      }
    } else if (content instanceof ArrayBuffer) {
      uploadContent = new Uint8Array(content);
    } else {
      uploadContent = content;
    }
    
    // 上传到R2
    await this.r2.put(r2Key, uploadContent, {
      httpMetadata: {
        contentType: contentType,
        contentDisposition: `attachment; filename="${sanitizedFilename}"`
      },
      customMetadata: {
        emailId: emailId,
        originalFilename: filename,
        uploadedAt: new Date().toISOString()
      }
    });
    
    return r2Key;
  }

  /**
   * 从R2存储下载附件
   * @param {string} r2Key R2对象键
   * @return {Promise<Object|null>} 附件对象或null
   */
  async downloadAttachment(r2Key) {
    try {
      const object = await this.r2.get(r2Key);
      
      if (!object) {
        return null;
      }
      
      return {
        body: object.body,
        contentType: object.httpMetadata?.contentType || 'application/octet-stream',
        contentDisposition: object.httpMetadata?.contentDisposition,
        size: object.size,
        metadata: object.customMetadata
      };
    } catch (error) {
      console.error('Error downloading attachment:', error);
      return null;
    }
  }

  /**
   * 删除R2存储中的附件
   * @param {string} r2Key R2对象键
   * @return {Promise<boolean>} 是否删除成功
   */
  async deleteAttachment(r2Key) {
    try {
      await this.r2.delete(r2Key);
      return true;
    } catch (error) {
      console.error('Error deleting attachment:', error);
      return false;
    }
  }

  /**
   * 批量删除R2存储中的附件
   * @param {Array<string>} r2Keys R2对象键数组
   * @return {Promise<number>} 成功删除的数量
   */
  async deleteAttachments(r2Keys) {
    if (r2Keys.length === 0) {
      return 0;
    }
    
    let deletedCount = 0;
    
    // 批量删除（R2支持批量删除操作）
    try {
      await this.r2.delete(r2Keys);
      deletedCount = r2Keys.length;
    } catch (error) {
      console.error('Batch delete failed, trying individual deletes:', error);
      
      // 如果批量删除失败，逐个删除
      for (const r2Key of r2Keys) {
        try {
          await this.r2.delete(r2Key);
          deletedCount++;
        } catch (individualError) {
          console.error(`Failed to delete ${r2Key}:`, individualError);
        }
      }
    }
    
    return deletedCount;
  }

  /**
   * 检查附件是否存在
   * @param {string} r2Key R2对象键
   * @return {Promise<boolean>} 是否存在
   */
  async attachmentExists(r2Key) {
    try {
      const object = await this.r2.head(r2Key);
      return !!object;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取附件信息（不下载内容）
   * @param {string} r2Key R2对象键
   * @return {Promise<Object|null>} 附件信息或null
   */
  async getAttachmentInfo(r2Key) {
    try {
      const object = await this.r2.head(r2Key);
      
      if (!object) {
        return null;
      }
      
      return {
        size: object.size,
        contentType: object.httpMetadata?.contentType,
        contentDisposition: object.httpMetadata?.contentDisposition,
        lastModified: object.uploaded,
        metadata: object.customMetadata
      };
    } catch (error) {
      console.error('Error getting attachment info:', error);
      return null;
    }
  }

  /**
   * 清理文件名，移除不安全字符
   * @param {string} filename 原始文件名
   * @return {string} 清理后的文件名
   */
  sanitizeFilename(filename) {
    // 移除路径分隔符和其他不安全字符
    return filename
      .replace(/[\/\\:*?"<>|]/g, '_')
      .replace(/\s+/g, '_')
      .substring(0, 255); // 限制文件名长度
  }

  /**
   * 根据文件扩展名确定内容类型
   * @param {string} filename 文件名
   * @return {string} MIME类型
   */
  getContentType(filename) {
    const extension = filename.toLowerCase().split('.').pop();
    
    const mimeTypes = {
      // 图片
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      
      // 文档
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      
      // 文本
      'txt': 'text/plain',
      'csv': 'text/csv',
      'html': 'text/html',
      'css': 'text/css',
      'js': 'application/javascript',
      'json': 'application/json',
      'xml': 'application/xml',
      
      // 压缩文件
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed',
      'tar': 'application/x-tar',
      'gz': 'application/gzip',
      
      // 音频
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',
      
      // 视频
      'mp4': 'video/mp4',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      'wmv': 'video/x-ms-wmv'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * 生成随机ID
   * @return {string} 随机ID
   */
  generateRandomId() {
    return Math.random().toString(36).substring(2, 15);
  }
}
