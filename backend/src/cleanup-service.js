/**
 * 清理服务
 * 定时清理过期数据和文件
 */

import { DatabaseService } from './database-service.js';
import { StorageService } from './storage-service.js';

export class CleanupService {
  /**
   * 构造函数
   * @param {Object} db D1数据库实例
   * @param {Object} r2 R2存储实例
   */
  constructor(db, r2) {
    this.dbService = new DatabaseService(db);
    this.storageService = new StorageService(r2);
  }

  /**
   * 执行完整的数据清理
   * @return {Promise<Object>} 清理结果统计
   */
  async cleanupExpiredData() {
    console.log('Starting cleanup process...');
    
    const startTime = Date.now();
    const results = {
      deletedEmailAddresses: 0,
      deletedEmails: 0,
      deletedAttachments: 0,
      errors: []
    };

    try {
      // 1. 清理过期的邮箱地址
      results.deletedEmailAddresses = await this.cleanupExpiredEmailAddresses();
      console.log(`Deleted ${results.deletedEmailAddresses} expired email addresses`);

      // 2. 清理24小时前的邮件和附件
      const emailCleanupResult = await this.cleanupOldEmails();
      results.deletedEmails = emailCleanupResult.deletedEmails;
      results.deletedAttachments = emailCleanupResult.deletedAttachments;
      console.log(`Deleted ${results.deletedEmails} old emails and ${results.deletedAttachments} attachments`);

      // 3. 清理孤立的附件（可选，作为安全措施）
      const orphanedAttachments = await this.cleanupOrphanedAttachments();
      results.deletedAttachments += orphanedAttachments;
      if (orphanedAttachments > 0) {
        console.log(`Deleted ${orphanedAttachments} orphaned attachments`);
      }

    } catch (error) {
      console.error('Cleanup process error:', error);
      results.errors.push(error.message);
    }

    const duration = Date.now() - startTime;
    console.log(`Cleanup completed in ${duration}ms:`, results);

    return results;
  }

  /**
   * 清理过期的邮箱地址
   * @return {Promise<number>} 删除的邮箱地址数量
   */
  async cleanupExpiredEmailAddresses() {
    try {
      return await this.dbService.deleteExpiredEmailAddresses();
    } catch (error) {
      console.error('Error cleaning up expired email addresses:', error);
      throw error;
    }
  }

  /**
   * 清理24小时前的邮件和相关附件
   * @return {Promise<Object>} 清理结果
   */
  async cleanupOldEmails() {
    try {
      // 获取要删除的邮件ID列表
      const emailIds = await this.dbService.deleteOldEmails();
      
      if (emailIds.length === 0) {
        return { deletedEmails: 0, deletedAttachments: 0 };
      }

      // 获取这些邮件的附件R2键列表
      const r2Keys = await this.dbService.getAttachmentR2Keys(emailIds);
      
      // 从R2存储中删除附件文件
      let deletedAttachments = 0;
      if (r2Keys.length > 0) {
        deletedAttachments = await this.storageService.deleteAttachments(r2Keys);
      }

      return {
        deletedEmails: emailIds.length,
        deletedAttachments: deletedAttachments
      };
    } catch (error) {
      console.error('Error cleaning up old emails:', error);
      throw error;
    }
  }

  /**
   * 清理孤立的附件（数据库中不存在对应记录的R2文件）
   * 这是一个安全措施，通常不应该有孤立附件
   * @return {Promise<number>} 删除的孤立附件数量
   */
  async cleanupOrphanedAttachments() {
    try {
      // 注意：这个功能需要谨慎实现，因为R2不支持直接列出所有对象
      // 在实际生产环境中，可能需要维护一个单独的索引来跟踪所有上传的文件
      
      // 目前返回0，表示没有清理孤立附件
      // 如果需要实现这个功能，可以考虑以下方案：
      // 1. 定期扫描R2存储桶中的文件
      // 2. 与数据库中的附件记录进行比对
      // 3. 删除数据库中不存在的文件
      
      return 0;
    } catch (error) {
      console.error('Error cleaning up orphaned attachments:', error);
      return 0;
    }
  }

  /**
   * 获取清理统计信息
   * @return {Promise<Object>} 统计信息
   */
  async getCleanupStats() {
    try {
      // 获取即将过期的邮箱地址数量
      const expiredEmailAddresses = await this.dbService.db.prepare(`
        SELECT COUNT(*) as count FROM email_addresses
        WHERE expires_at <= datetime('now')
      `).first();

      // 获取24小时前的邮件数量
      const oldEmails = await this.dbService.db.prepare(`
        SELECT COUNT(*) as count FROM emails
        WHERE received_at <= datetime('now', '-24 hours')
      `).first();

      // 获取相关附件数量
      const oldAttachments = await this.dbService.db.prepare(`
        SELECT COUNT(*) as count FROM attachments
        WHERE email_id IN (
          SELECT id FROM emails
          WHERE received_at <= datetime('now', '-24 hours')
        )
      `).first();

      // 获取总的存储使用情况
      const totalEmails = await this.dbService.db.prepare(`
        SELECT COUNT(*) as count FROM emails
      `).first();

      const totalAttachments = await this.dbService.db.prepare(`
        SELECT COUNT(*) as count, SUM(size) as total_size FROM attachments
      `).first();

      return {
        expiredEmailAddresses: expiredEmailAddresses?.count || 0,
        oldEmails: oldEmails?.count || 0,
        oldAttachments: oldAttachments?.count || 0,
        totalEmails: totalEmails?.count || 0,
        totalAttachments: totalAttachments?.count || 0,
        totalAttachmentSize: totalAttachments?.total_size || 0
      };
    } catch (error) {
      console.error('Error getting cleanup stats:', error);
      return {
        expiredEmailAddresses: 0,
        oldEmails: 0,
        oldAttachments: 0,
        totalEmails: 0,
        totalAttachments: 0,
        totalAttachmentSize: 0
      };
    }
  }

  /**
   * 手动触发清理（用于测试或紧急情况）
   * @param {Object} options 清理选项
   * @return {Promise<Object>} 清理结果
   */
  async manualCleanup(options = {}) {
    const {
      cleanupEmailAddresses = true,
      cleanupEmails = true,
      cleanupAttachments = true,
      forceCleanup = false
    } = options;

    console.log('Starting manual cleanup with options:', options);
    
    const results = {
      deletedEmailAddresses: 0,
      deletedEmails: 0,
      deletedAttachments: 0,
      errors: []
    };

    try {
      if (cleanupEmailAddresses) {
        results.deletedEmailAddresses = await this.cleanupExpiredEmailAddresses();
      }

      if (cleanupEmails) {
        const emailCleanupResult = await this.cleanupOldEmails();
        results.deletedEmails = emailCleanupResult.deletedEmails;
        
        if (cleanupAttachments) {
          results.deletedAttachments = emailCleanupResult.deletedAttachments;
        }
      }

      if (forceCleanup) {
        // 强制清理模式下的额外操作
        const orphanedAttachments = await this.cleanupOrphanedAttachments();
        results.deletedAttachments += orphanedAttachments;
      }

    } catch (error) {
      console.error('Manual cleanup error:', error);
      results.errors.push(error.message);
    }

    return results;
  }
}
