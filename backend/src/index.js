/**
 * 临时邮箱系统 Cloudflare Worker
 * 遵循Google JavaScript风格指南
 */

import { EmailParser } from './email-parser.js';
import { DatabaseService } from './database-service.js';
import { StorageService } from './storage-service.js';
import { CleanupService } from './cleanup-service.js';

/**
 * Worker主入口点
 */
export default {
  /**
   * 处理HTTP请求
   * @param {Request} request 请求对象
   * @param {Object} env 环境变量
   * @param {Object} ctx 执行上下文
   * @return {Promise<Response>} 响应对象
   */
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    // CORS处理
    if (method === 'OPTIONS') {
      return handleCors(request, env);
    }

    try {
      // 路由处理
      if (path === '/api/generate-email' && method === 'POST') {
        return await handleGenerateEmail(request, env);
      } else if (path.startsWith('/api/emails/') && method === 'GET') {
        return await handleGetEmails(request, env);
      } else if (path.startsWith('/api/email/') && method === 'GET') {
        return await handleGetEmailDetail(request, env);
      } else if (path.startsWith('/api/attachment/') && path.endsWith('/download') && method === 'GET') {
        return await handleDownloadAttachment(request, env);
      } else if (path === '/api/cleanup/stats' && method === 'GET') {
        return await handleGetCleanupStats(request, env);
      } else if (path === '/api/cleanup/manual' && method === 'POST') {
        return await handleManualCleanup(request, env);
      } else if (path === '/email' && method === 'POST') {
        return await handleIncomingEmail(request, env);
      } else {
        return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('Worker error:', error);
      return new Response(
        JSON.stringify({ error: 'Internal Server Error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  },

  /**
   * 处理定时任务
   * @param {Object} event 定时事件
   * @param {Object} env 环境变量
   * @param {Object} ctx 执行上下文
   */
  async scheduled(event, env, ctx) {
    console.log('Running scheduled cleanup task');
    const cleanupService = new CleanupService(env.DB, env.R2);
    await cleanupService.cleanupExpiredData();
  }
};

/**
 * 处理CORS预检请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Response} CORS响应
 */
function handleCors(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': env.CORS_ORIGIN || '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Max-Age': '86400',
  };

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

/**
 * 添加CORS头到响应
 * @param {Response} response 原始响应
 * @param {Object} env 环境变量
 * @return {Response} 添加了CORS头的响应
 */
function addCorsHeaders(response, env) {
  const newResponse = new Response(response.body, response);
  newResponse.headers.set('Access-Control-Allow-Origin', env.CORS_ORIGIN || '*');
  newResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  newResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type');
  return newResponse;
}

/**
 * 生成新的临时邮箱地址
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleGenerateEmail(request, env) {
  const dbService = new DatabaseService(env.DB);
  
  // 生成随机邮箱地址
  const randomId = generateRandomId();
  const emailAddress = `${randomId}@${env.DOMAIN}`;
  
  // 设置24小时后过期
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
  
  await dbService.createEmailAddress(emailAddress, expiresAt);
  
  const response = new Response(
    JSON.stringify({ email: emailAddress }),
    {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }
  );
  
  return addCorsHeaders(response, env);
}

/**
 * 获取邮件列表
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleGetEmails(request, env) {
  const url = new URL(request.url);
  const emailAddress = decodeURIComponent(url.pathname.split('/').pop());
  
  const dbService = new DatabaseService(env.DB);
  const emails = await dbService.getEmailsByRecipient(emailAddress);
  
  const response = new Response(
    JSON.stringify({ emails }),
    {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }
  );
  
  return addCorsHeaders(response, env);
}

/**
 * 获取邮件详情
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleGetEmailDetail(request, env) {
  const url = new URL(request.url);
  const emailId = url.pathname.split('/').pop();
  
  const dbService = new DatabaseService(env.DB);
  const email = await dbService.getEmailById(emailId);
  
  if (!email) {
    return new Response(
      JSON.stringify({ error: 'Email not found' }),
      {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  
  // 获取附件信息
  const attachments = await dbService.getAttachmentsByEmailId(emailId);
  email.attachments = attachments;
  
  const response = new Response(
    JSON.stringify(email),
    {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }
  );
  
  return addCorsHeaders(response, env);
}

/**
 * 处理接收到的邮件
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleIncomingEmail(request, env) {
  const emailParser = new EmailParser();
  const dbService = new DatabaseService(env.DB);
  const storageService = new StorageService(env.R2);
  
  // 解析邮件内容
  const rawEmail = await request.text();
  const parsedEmail = await emailParser.parse(rawEmail);
  
  // 检查收件人是否存在且有效
  const isValidRecipient = await dbService.isValidEmailAddress(parsedEmail.to);
  if (!isValidRecipient) {
    return new Response('Invalid recipient', { status: 400 });
  }
  
  // 保存邮件到数据库
  const emailId = await dbService.saveEmail(parsedEmail);
  
  // 处理附件
  if (parsedEmail.attachments && parsedEmail.attachments.length > 0) {
    for (const attachment of parsedEmail.attachments) {
      const r2Key = await storageService.uploadAttachment(
        emailId,
        attachment.filename,
        attachment.content
      );
      
      await dbService.saveAttachment({
        emailId,
        filename: attachment.filename,
        contentType: attachment.contentType,
        size: attachment.size,
        r2Key
      });
    }
  }
  
  return new Response('Email processed successfully', { status: 200 });
}

/**
 * 处理附件下载
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleDownloadAttachment(request, env) {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const attachmentId = pathParts[pathParts.length - 2]; // /api/attachment/{id}/download

  const dbService = new DatabaseService(env.DB);
  const storageService = new StorageService(env.R2);

  // 获取附件信息
  const attachment = await dbService.getAttachmentById(attachmentId);
  if (!attachment) {
    return new Response('Attachment not found', { status: 404 });
  }

  // 从R2下载附件
  const attachmentData = await storageService.downloadAttachment(attachment.r2_key);
  if (!attachmentData) {
    return new Response('Attachment file not found', { status: 404 });
  }

  return new Response(attachmentData.body, {
    headers: {
      'Content-Type': attachmentData.contentType,
      'Content-Disposition': attachmentData.contentDisposition || `attachment; filename="${attachment.filename}"`,
      'Content-Length': attachmentData.size.toString()
    }
  });
}

/**
 * 获取清理统计信息
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleGetCleanupStats(request, env) {
  const cleanupService = new CleanupService(env.DB, env.R2);
  const stats = await cleanupService.getCleanupStats();

  const response = new Response(
    JSON.stringify(stats),
    {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }
  );

  return addCorsHeaders(response, env);
}

/**
 * 手动触发清理
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @return {Promise<Response>} 响应对象
 */
async function handleManualCleanup(request, env) {
  const options = await request.json().catch(() => ({}));
  const cleanupService = new CleanupService(env.DB, env.R2);
  const results = await cleanupService.manualCleanup(options);

  const response = new Response(
    JSON.stringify(results),
    {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }
  );

  return addCorsHeaders(response, env);
}

/**
 * 生成随机ID
 * @return {string} 随机ID
 */
function generateRandomId() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
