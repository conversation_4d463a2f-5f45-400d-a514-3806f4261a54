/**
 * 邮件解析器
 * 解析原始邮件内容并提取结构化数据
 */

export class EmailParser {
  /**
   * 解析原始邮件内容
   * @param {string} rawEmail 原始邮件内容
   * @return {Promise<Object>} 解析后的邮件对象
   */
  async parse(rawEmail) {
    const lines = rawEmail.split('\n');
    const headers = {};
    const attachments = [];
    let headersParsed = false;
    let currentHeader = '';
    let bodyStart = 0;
    
    // 解析邮件头
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (!headersParsed) {
        if (line.trim() === '') {
          headersParsed = true;
          bodyStart = i + 1;
          break;
        }
        
        if (line.startsWith(' ') || line.startsWith('\t')) {
          // 继续上一个头部字段
          if (currentHeader) {
            headers[currentHeader] += ' ' + line.trim();
          }
        } else {
          const colonIndex = line.indexOf(':');
          if (colonIndex > 0) {
            currentHeader = line.substring(0, colonIndex).toLowerCase().trim();
            headers[currentHeader] = line.substring(colonIndex + 1).trim();
          }
        }
      }
    }
    
    // 获取邮件体
    const body = lines.slice(bodyStart).join('\n');
    
    // 解析内容类型
    const contentType = headers['content-type'] || '';
    const boundary = this.extractBoundary(contentType);
    
    let textContent = '';
    let htmlContent = '';
    
    if (boundary) {
      // 多部分邮件
      const parts = this.parseMultipart(body, boundary);
      for (const part of parts) {
        if (part.contentType.includes('text/plain')) {
          textContent = part.content;
        } else if (part.contentType.includes('text/html')) {
          htmlContent = part.content;
        } else if (part.filename) {
          // 附件
          attachments.push({
            filename: part.filename,
            contentType: part.contentType,
            content: part.content,
            size: part.content.length
          });
        }
      }
    } else {
      // 单部分邮件
      if (contentType.includes('text/html')) {
        htmlContent = body;
      } else {
        textContent = body;
      }
    }
    
    return {
      messageId: headers['message-id'] || this.generateMessageId(),
      from: this.parseEmailAddress(headers['from'] || ''),
      to: this.parseEmailAddress(headers['to'] || ''),
      subject: this.decodeHeader(headers['subject'] || ''),
      date: headers['date'] || new Date().toISOString(),
      inReplyTo: headers['in-reply-to'],
      references: headers['references'],
      textContent: textContent.trim(),
      htmlContent: htmlContent.trim(),
      attachments
    };
  }
  
  /**
   * 提取边界字符串
   * @param {string} contentType Content-Type头部值
   * @return {string|null} 边界字符串
   */
  extractBoundary(contentType) {
    const boundaryMatch = contentType.match(/boundary=([^;]+)/i);
    if (boundaryMatch) {
      return boundaryMatch[1].replace(/['"]/g, '');
    }
    return null;
  }
  
  /**
   * 解析多部分邮件
   * @param {string} body 邮件体
   * @param {string} boundary 边界字符串
   * @return {Array<Object>} 邮件部分数组
   */
  parseMultipart(body, boundary) {
    const parts = [];
    const sections = body.split(`--${boundary}`);
    
    for (let i = 1; i < sections.length - 1; i++) {
      const section = sections[i];
      const lines = section.split('\n');
      const partHeaders = {};
      let partHeadersParsed = false;
      let partBodyStart = 0;
      let currentPartHeader = '';
      
      // 解析部分头部
      for (let j = 0; j < lines.length; j++) {
        const line = lines[j];
        
        if (!partHeadersParsed) {
          if (line.trim() === '') {
            partHeadersParsed = true;
            partBodyStart = j + 1;
            break;
          }
          
          if (line.startsWith(' ') || line.startsWith('\t')) {
            if (currentPartHeader) {
              partHeaders[currentPartHeader] += ' ' + line.trim();
            }
          } else {
            const colonIndex = line.indexOf(':');
            if (colonIndex > 0) {
              currentPartHeader = line.substring(0, colonIndex).toLowerCase().trim();
              partHeaders[currentPartHeader] = line.substring(colonIndex + 1).trim();
            }
          }
        }
      }
      
      const partBody = lines.slice(partBodyStart).join('\n').trim();
      const contentType = partHeaders['content-type'] || 'text/plain';
      const contentDisposition = partHeaders['content-disposition'] || '';
      const contentTransferEncoding = partHeaders['content-transfer-encoding'] || '';
      
      // 提取文件名
      let filename = null;
      const filenameMatch = contentDisposition.match(/filename=([^;]+)/i);
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
      
      // 解码内容
      let decodedContent = partBody;
      if (contentTransferEncoding.toLowerCase() === 'base64') {
        try {
          decodedContent = atob(partBody.replace(/\s/g, ''));
        } catch (error) {
          console.error('Base64 decode error:', error);
        }
      } else if (contentTransferEncoding.toLowerCase() === 'quoted-printable') {
        decodedContent = this.decodeQuotedPrintable(partBody);
      }
      
      parts.push({
        contentType,
        contentDisposition,
        filename,
        content: decodedContent
      });
    }
    
    return parts;
  }
  
  /**
   * 解析邮件地址
   * @param {string} addressString 邮件地址字符串
   * @return {string} 清理后的邮件地址
   */
  parseEmailAddress(addressString) {
    const emailMatch = addressString.match(/<([^>]+)>/);
    if (emailMatch) {
      return emailMatch[1];
    }
    
    const simpleEmailMatch = addressString.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (simpleEmailMatch) {
      return simpleEmailMatch[1];
    }
    
    return addressString.trim();
  }
  
  /**
   * 解码邮件头部
   * @param {string} header 头部值
   * @return {string} 解码后的头部值
   */
  decodeHeader(header) {
    // 简单的RFC 2047解码实现
    return header.replace(/=\?([^?]+)\?([BQ])\?([^?]+)\?=/gi, (match, charset, encoding, encodedText) => {
      try {
        if (encoding.toUpperCase() === 'B') {
          return atob(encodedText);
        } else if (encoding.toUpperCase() === 'Q') {
          return this.decodeQuotedPrintable(encodedText.replace(/_/g, ' '));
        }
      } catch (error) {
        console.error('Header decode error:', error);
      }
      return match;
    });
  }
  
  /**
   * 解码Quoted-Printable编码
   * @param {string} str 编码的字符串
   * @return {string} 解码后的字符串
   */
  decodeQuotedPrintable(str) {
    return str.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
      return String.fromCharCode(parseInt(hex, 16));
    }).replace(/=\r?\n/g, '');
  }
  
  /**
   * 生成消息ID
   * @return {string} 消息ID
   */
  generateMessageId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `<${timestamp}.${random}@temp-email>`;
  }
}
