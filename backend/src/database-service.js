/**
 * 数据库服务
 * 处理与Cloudflare D1数据库的交互
 */

export class DatabaseService {
  /**
   * 构造函数
   * @param {Object} db D1数据库实例
   */
  constructor(db) {
    this.db = db;
  }

  /**
   * 创建新的邮箱地址
   * @param {string} emailAddress 邮箱地址
   * @param {Date} expiresAt 过期时间
   * @return {Promise<string>} 邮箱地址ID
   */
  async createEmailAddress(emailAddress, expiresAt) {
    const id = this.generateId();
    
    await this.db.prepare(`
      INSERT INTO email_addresses (id, email_address, expires_at)
      VALUES (?, ?, ?)
    `).bind(id, emailAddress, expiresAt.toISOString()).run();
    
    return id;
  }

  /**
   * 检查邮箱地址是否有效
   * @param {string} emailAddress 邮箱地址
   * @return {Promise<boolean>} 是否有效
   */
  async isValidEmailAddress(emailAddress) {
    const result = await this.db.prepare(`
      SELECT id FROM email_addresses
      WHERE email_address = ? AND expires_at > datetime('now') AND is_active = TRUE
    `).bind(emailAddress).first();
    
    return !!result;
  }

  /**
   * 保存邮件
   * @param {Object} emailData 邮件数据
   * @return {Promise<string>} 邮件ID
   */
  async saveEmail(emailData) {
    const id = this.generateId();
    
    await this.db.prepare(`
      INSERT INTO emails (
        id, recipient, sender, subject, content_text, content_html,
        message_id, in_reply_to, references
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      emailData.to,
      emailData.from,
      emailData.subject,
      emailData.textContent,
      emailData.htmlContent,
      emailData.messageId,
      emailData.inReplyTo,
      emailData.references
    ).run();
    
    return id;
  }

  /**
   * 根据收件人获取邮件列表
   * @param {string} recipient 收件人邮箱地址
   * @return {Promise<Array<Object>>} 邮件列表
   */
  async getEmailsByRecipient(recipient) {
    const results = await this.db.prepare(`
      SELECT 
        id,
        sender,
        subject,
        SUBSTR(COALESCE(content_text, content_html), 1, 200) as preview,
        received_at
      FROM emails
      WHERE recipient = ?
      ORDER BY received_at DESC
      LIMIT 50
    `).bind(recipient).all();
    
    return results.results || [];
  }

  /**
   * 根据ID获取邮件详情
   * @param {string} emailId 邮件ID
   * @return {Promise<Object|null>} 邮件详情
   */
  async getEmailById(emailId) {
    const result = await this.db.prepare(`
      SELECT 
        id,
        recipient,
        sender,
        subject,
        content_text,
        content_html,
        received_at,
        message_id,
        in_reply_to,
        references
      FROM emails
      WHERE id = ?
    `).bind(emailId).first();
    
    if (!result) {
      return null;
    }
    
    // 优先返回HTML内容，如果没有则返回文本内容
    result.content = result.content_html || result.content_text || '';
    
    return result;
  }

  /**
   * 保存附件信息
   * @param {Object} attachmentData 附件数据
   * @return {Promise<string>} 附件ID
   */
  async saveAttachment(attachmentData) {
    const id = this.generateId();
    
    await this.db.prepare(`
      INSERT INTO attachments (id, email_id, filename, content_type, size, r2_key)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      attachmentData.emailId,
      attachmentData.filename,
      attachmentData.contentType,
      attachmentData.size,
      attachmentData.r2Key
    ).run();
    
    return id;
  }

  /**
   * 根据邮件ID获取附件列表
   * @param {string} emailId 邮件ID
   * @return {Promise<Array<Object>>} 附件列表
   */
  async getAttachmentsByEmailId(emailId) {
    const results = await this.db.prepare(`
      SELECT id, filename, content_type, size, r2_key
      FROM attachments
      WHERE email_id = ?
      ORDER BY created_at
    `).bind(emailId).all();
    
    // 为每个附件生成下载URL
    const attachments = (results.results || []).map(attachment => ({
      ...attachment,
      url: `/api/attachment/${attachment.id}/download`
    }));
    
    return attachments;
  }

  /**
   * 根据附件ID获取附件信息
   * @param {string} attachmentId 附件ID
   * @return {Promise<Object|null>} 附件信息
   */
  async getAttachmentById(attachmentId) {
    const result = await this.db.prepare(`
      SELECT id, email_id, filename, content_type, size, r2_key
      FROM attachments
      WHERE id = ?
    `).bind(attachmentId).first();
    
    return result;
  }

  /**
   * 删除过期的邮箱地址
   * @return {Promise<number>} 删除的记录数
   */
  async deleteExpiredEmailAddresses() {
    const result = await this.db.prepare(`
      DELETE FROM email_addresses
      WHERE expires_at <= datetime('now')
    `).run();
    
    return result.changes || 0;
  }

  /**
   * 删除24小时前的邮件
   * @return {Promise<Array<string>>} 被删除邮件的ID列表
   */
  async deleteOldEmails() {
    // 首先获取要删除的邮件ID列表
    const emailsToDelete = await this.db.prepare(`
      SELECT id FROM emails
      WHERE received_at <= datetime('now', '-24 hours')
    `).all();
    
    const emailIds = (emailsToDelete.results || []).map(email => email.id);
    
    if (emailIds.length === 0) {
      return [];
    }
    
    // 删除邮件（附件会通过外键约束自动删除）
    const placeholders = emailIds.map(() => '?').join(',');
    await this.db.prepare(`
      DELETE FROM emails
      WHERE id IN (${placeholders})
    `).bind(...emailIds).run();
    
    return emailIds;
  }

  /**
   * 获取需要删除的附件R2键列表
   * @param {Array<string>} emailIds 邮件ID列表
   * @return {Promise<Array<string>>} R2键列表
   */
  async getAttachmentR2Keys(emailIds) {
    if (emailIds.length === 0) {
      return [];
    }
    
    const placeholders = emailIds.map(() => '?').join(',');
    const results = await this.db.prepare(`
      SELECT r2_key FROM attachments
      WHERE email_id IN (${placeholders})
    `).bind(...emailIds).all();
    
    return (results.results || []).map(attachment => attachment.r2_key);
  }

  /**
   * 生成唯一ID
   * @return {string} 唯一ID
   */
  generateId() {
    return crypto.randomUUID();
  }
}
