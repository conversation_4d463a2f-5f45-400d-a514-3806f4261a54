# Cloudflare Worker 配置文件
name = "temp-email-worker"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 环境变量
[vars]
DOMAIN = "your-domain.com"  # 替换为您的域名
CORS_ORIGIN = "https://your-frontend-domain.pages.dev"  # 替换为前端域名

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "temp-email-db"
database_id = "your-database-id"  # 创建数据库后替换

# R2 存储绑定
[[r2_buckets]]
binding = "R2"
bucket_name = "temp-email-attachments"

# 定时任务 - 每小时清理过期数据
[[triggers]]
crons = ["0 * * * *"]  # 每小时的第0分钟执行

# 邮件路由配置
# 需要在 Cloudflare Dashboard 中配置 Email Routing
# 将邮件转发到这个 Worker 的 /email 端点
